package convertz

import "reflect"

// ============================================================================
// 保持向后兼容的公共API
// ============================================================================

// indirect 获取值的实际类型 - 处理指针和接口
func indirect(i any) (any, bool) {
	if i == nil {
		return nil, false
	}
	v := reflect.ValueOf(i)
	x := false
	for v.Kind() == reflect.Ptr || v.Kind() == reflect.Interface {
		if v.IsNil() {
			return nil, x
		}
		x = true
		v = v.Elem()
	}
	return v.Interface(), x
}

// to 内部转换函数 - 保持原有签名
func to(src any, dst any, opts ...Option) error {
	src, ok := indirect(src)
	if !ok {
		return ErrUnsupported
	}

	opt := DefaultOptions()
	for _, o := range opts {
		o(opt)
	}

	var err error
	switch dst := dst.(type) {
	case *bool:
		*dst, err = convertTo[bool](src, opt)
	case *int:
		*dst, err = convertTo[int](src, opt)
	case *int8:
		*dst, err = convertTo[int8](src, opt)
	case *int16:
		*dst, err = convertTo[int16](src, opt)
	case *int32:
		*dst, err = convertTo[int32](src, opt)
	case *int64:
		*dst, err = convertTo[int64](src, opt)
	case *uint:
		*dst, err = convertTo[uint](src, opt)
	case *uint8:
		*dst, err = convertTo[uint8](src, opt)
	case *uint16:
		*dst, err = convertTo[uint16](src, opt)
	case *uint32:
		*dst, err = convertTo[uint32](src, opt)
	case *uint64:
		*dst, err = convertTo[uint64](src, opt)
	case *uintptr:
		*dst, err = convertTo[uintptr](src, opt)
	case *float32:
		*dst, err = convertTo[float32](src, opt)
	case *float64:
		*dst, err = convertTo[float64](src, opt)
	case *complex64:
		*dst, err = convertTo[complex64](src, opt)
	case *complex128:
		*dst, err = convertTo[complex128](src, opt)
	case *string:
		*dst, err = convertTo[string](src, opt)
	case *[]byte:
		*dst, err = convertTo[[]byte](src, opt)
	case *[]rune:
		*dst, err = convertTo[[]rune](src, opt)
	default:
		return ErrUnsupported
	}
	return err
}

// ============================================================================
// 主要公共API - 保持完全向后兼容
// ============================================================================

// ToBoolean 转换为布尔类型 - 保持原有签名
func ToBoolean[D Boolean](src any, opt *BooleanOptions) (D, error) {
	opts := DefaultOptions()
	if opt != nil {
		opts.Boolean = opt
	}
	return convertTo[D](src, opts)
}

// ToNumeric 转换为数值类型 - 保持原有签名
func ToNumeric[D Numeric](src any, opt *NumericOptions) (D, error) {
	opts := DefaultOptions()
	if opt != nil {
		opts.Numeric = opt
	}
	return convertTo[D](src, opts)
}

// ToTextual 转换为文本类型 - 保持原有签名
func ToTextual[D Textual](src any, opt *TextualOptions) (D, error) {
	opts := DefaultOptions()
	if opt != nil {
		opts.Textual = opt
	}
	return convertTo[D](src, opts)
}

// ToComplex 转换为复数类型 - 保持原有签名
func ToComplex[D Complex](src any, opt *ComplexOptions) (D, error) {
	opts := DefaultOptions()
	if opt != nil {
		opts.Complex = opt
	}
	return convertTo[D](src, opts)
}

// ============================================================================
// 通用转换函数
// ============================================================================

// To 转换为指定类型 - 泛型版本
func To[D any](src any, opts ...Option) (D, error) {
	opt := DefaultOptions()
	for _, o := range opts {
		o(opt)
	}
	return convertTo[D](src, opt)
}

// Try 转换为指定类型，失败时返回默认值
func Try[D any](src any, def D, opts ...Option) D {
	result, err := To[D](src, opts...)
	if err != nil {
		return def
	}
	return result
}

// Must 转换为指定类型，失败时panic
func Must[D any](src any, opts ...Option) D {
	result, err := To[D](src, opts...)
	if err != nil {
		panic(err)
	}
	return result
}
