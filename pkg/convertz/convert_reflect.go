package convertz

import (
	"reflect"
)

// 反射转换 - 作为最后的兜底方案
func reflectConvert[D any](src any, opts *Options) (D, error) {
	var zero D
	dstType := reflect.TypeOf(zero)
	srcValue := reflect.ValueOf(src)

	// 尝试直接类型转换
	if srcValue.Type().ConvertibleTo(dstType) {
		converted := srcValue.Convert(dstType)
		return converted.Interface().(D), nil
	}

	// 根据目标类型进行反射转换
	switch dstType.Kind() {
	case reflect.Bool:
		result, err := reflectToBoolean(src, opts)
		return any(result).(D), err
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return reflectToSignedInt[D](srcValue, opts)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return reflectToUnsignedInt[D](srcValue, opts)
	case reflect.Float32, reflect.Float64:
		return reflectToFloat[D](srcValue, opts)
	case reflect.Complex64, reflect.Complex128:
		return reflectToComplex[D](srcValue, opts)
	case reflect.String:
		result, err := reflectToString(srcValue, opts)
		return any(result).(D), err
	case reflect.Slice:
		if dstType.Elem().Kind() == reflect.Uint8 {
			result, err := reflectToBytes(srcValue, opts)
			return any(result).(D), err
		} else if dstType.Elem().Kind() == reflect.Int32 {
			result, err := reflectToRunes(srcValue, opts)
			return any(result).(D), err
		}
	}

	return zero, ErrUnsupported
}

// 反射到布尔值
func reflectToBoolean(src any, opts *Options) (bool, error) {
	v := reflect.ValueOf(src)

	switch v.Kind() {
	case reflect.Bool:
		return v.Bool(), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() != 0, nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() != 0, nil
	case reflect.Float32, reflect.Float64:
		return v.Float() != 0, nil
	case reflect.Complex64, reflect.Complex128:
		return v.Complex() != 0, nil
	case reflect.String:
		return textual2boolean[string, bool](v.String(), opts.Boolean)
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			return textual2boolean[[]byte, bool](v.Bytes(), opts.Boolean)
		}
	}

	return false, ErrUnsupported
}

// 反射到有符号整数
func reflectToSignedInt[D Numeric](v reflect.Value, opts *Options) (D, error) {
	switch v.Kind() {
	case reflect.Bool:
		if v.Bool() {
			return D(1), nil
		}
		return D(0), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return D(v.Int()), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return D(v.Uint()), nil
	case reflect.Float32, reflect.Float64:
		return D(v.Float()), nil
	case reflect.String:
		dstInfo := getTypeInfo[D]()
		return textual2numeric[string, D](v.String(), opts.Numeric, dstInfo)
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			dstInfo := getTypeInfo[D]()
			return textual2numeric[[]byte, D](v.Bytes(), opts.Numeric, dstInfo)
		}
	}

	return D(0), ErrUnsupported
}

// 反射到无符号整数
func reflectToUnsignedInt[D Numeric](v reflect.Value, opts *Options) (D, error) {
	switch v.Kind() {
	case reflect.Bool:
		if v.Bool() {
			return D(1), nil
		}
		return D(0), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return D(v.Int()), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return D(v.Uint()), nil
	case reflect.Float32, reflect.Float64:
		return D(v.Float()), nil
	case reflect.String:
		dstInfo := getTypeInfo[D]()
		return textual2numeric[string, D](v.String(), opts.Numeric, dstInfo)
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			dstInfo := getTypeInfo[D]()
			return textual2numeric[[]byte, D](v.Bytes(), opts.Numeric, dstInfo)
		}
	}

	return D(0), ErrUnsupported
}

// 反射到浮点数
func reflectToFloat[D Numeric](v reflect.Value, opts *Options) (D, error) {
	switch v.Kind() {
	case reflect.Bool:
		if v.Bool() {
			return D(1), nil
		}
		return D(0), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return D(v.Int()), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return D(v.Uint()), nil
	case reflect.Float32, reflect.Float64:
		return D(v.Float()), nil
	case reflect.String:
		dstInfo := getTypeInfo[D]()
		return textual2numeric[string, D](v.String(), opts.Numeric, dstInfo)
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			dstInfo := getTypeInfo[D]()
			return textual2numeric[[]byte, D](v.Bytes(), opts.Numeric, dstInfo)
		}
	}

	return D(0), ErrUnsupported
}

// 反射到复数
func reflectToComplex[D Complex](v reflect.Value, opts *Options) (D, error) {
	switch v.Kind() {
	case reflect.Bool:
		if v.Bool() {
			return D(1 + 0i), nil
		}
		return D(0 + 0i), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return D(complex(float64(v.Int()), 0)), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return D(complex(float64(v.Uint()), 0)), nil
	case reflect.Float32, reflect.Float64:
		return D(complex(v.Float(), 0)), nil
	case reflect.Complex64, reflect.Complex128:
		return D(v.Complex()), nil
	case reflect.String:
		return textual2complex[string, D](v.String())
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			return textual2complex[[]byte, D](v.Bytes())
		}
	}

	return D(0), ErrUnsupported
}

// 反射到字符串
func reflectToString(v reflect.Value, opts *Options) (string, error) {
	switch v.Kind() {
	case reflect.Bool:
		return boolean2textual[bool, string](v.Bool())
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return numeric2textual[int64, string](v.Int())
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return numeric2textual[uint64, string](v.Uint())
	case reflect.Float32, reflect.Float64:
		return numeric2textual[float64, string](v.Float())
	case reflect.Complex64, reflect.Complex128:
		return complex2textual[complex128, string](v.Complex())
	case reflect.String:
		return v.String(), nil
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			return textual2textual[[]byte, string](v.Bytes())
		} else if v.Type().Elem().Kind() == reflect.Int32 {
			runes := v.Interface().([]rune)
			return textual2textual[[]rune, string](runes)
		}
	}

	return "", ErrUnsupported
}

// 反射到字节切片
func reflectToBytes(v reflect.Value, opts *Options) ([]byte, error) {
	switch v.Kind() {
	case reflect.String:
		return textual2textual[string, []byte](v.String())
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Uint8 {
			return v.Bytes(), nil
		} else if v.Type().Elem().Kind() == reflect.Int32 {
			runes := v.Interface().([]rune)
			str, _ := textual2textual[[]rune, string](runes)
			return textual2textual[string, []byte](str)
		}
	}

	// 其他类型先转为字符串再转为字节
	str, err := reflectToString(v, opts)
	if err != nil {
		return nil, err
	}
	return textual2textual[string, []byte](str)
}

// 反射到符文切片
func reflectToRunes(v reflect.Value, opts *Options) ([]rune, error) {
	switch v.Kind() {
	case reflect.String:
		return textual2textual[string, []rune](v.String())
	case reflect.Slice:
		if v.Type().Elem().Kind() == reflect.Int32 {
			return v.Interface().([]rune), nil
		} else if v.Type().Elem().Kind() == reflect.Uint8 {
			str, _ := textual2textual[[]byte, string](v.Bytes())
			return textual2textual[string, []rune](str)
		}
	}

	// 其他类型先转为字符串再转为符文
	str, err := reflectToString(v, opts)
	if err != nil {
		return nil, err
	}
	return textual2textual[string, []rune](str)
}

// 反射到文本类型
func reflectToTextual[D Textual](src any, opts *Options) (D, error) {
	v := reflect.ValueOf(src)
	var zero D

	switch any(zero).(type) {
	case string:
		result, err := reflectToString(v, opts)
		return any(result).(D), err
	case []byte:
		result, err := reflectToBytes(v, opts)
		return any(result).(D), err
	case []rune:
		result, err := reflectToRunes(v, opts)
		return any(result).(D), err
	default:
		return zero, ErrUnsupported
	}
}
