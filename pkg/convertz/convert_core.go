package convertz

import (
	"reflect"
	"sync"
)

// 转换缓存 - 提升重复转换的性能
type conversionCache struct {
	mu    sync.RWMutex
	cache map[cacheKey]reflect.Type
}

type cacheKey struct {
	src, dst reflect.Type
}

var globalCache = &conversionCache{
	cache: make(map[cacheKey]reflect.Type),
}

// 核心转换分发器 - 利用泛型的编译时优化
func convertTo[D any](src any, opts *Options) (D, error) {
	var zero D

	// 快速路径1: 相同类型直接返回 (零成本)
	if result, ok := src.(D); ok {
		return result, nil
	}

	// 快速路径2: nil值处理
	if src == nil {
		return zero, ErrInvalidSrc
	}

	// 获取编译时类型信息
	dstInfo := getTypeInfo[D]()

	// 类型分发 - 基于编译时信息，避免运行时反射
	switch dstInfo.Kind {
	case KindBool:
		result, err := convertToBoolean(src, opts)
		return any(result).(D), err

	case KindSignedInt, KindUnsignedInt, KindFloat:
		return convertToNumeric[D](src, opts, dstInfo)

	case KindComplex:
		return convertToComplex[D](src, opts)

	case KindString, KindBytes, KindRunes:
		return convertToTextual[D](src, opts)

	default:
		// 最后的反射兜底
		return reflectConvert[D](src, opts)
	}
}

// 布尔转换统一处理
func convertToBoolean(src any, opts *Options) (bool, error) {
	switch s := src.(type) {
	// 直接类型匹配 - 最快路径
	case bool:
		return s, nil

	// 数值类型转换 - 使用泛型函数
	case int:
		return numericToBoolean(s), nil
	case int8:
		return numericToBoolean(s), nil
	case int16:
		return numericToBoolean(s), nil
	case int32:
		return numericToBoolean(s), nil
	case int64:
		return numericToBoolean(s), nil
	case uint:
		return numericToBoolean(s), nil
	case uint8:
		return numericToBoolean(s), nil
	case uint16:
		return numericToBoolean(s), nil
	case uint32:
		return numericToBoolean(s), nil
	case uint64:
		return numericToBoolean(s), nil
	case uintptr:
		return numericToBoolean(s), nil
	case float32:
		return numericToBoolean(s), nil
	case float64:
		return numericToBoolean(s), nil

	// 复数类型转换
	case complex64:
		return complexToBoolean(s), nil
	case complex128:
		return complexToBoolean(s), nil

	// 文本类型转换
	case string:
		return textual2boolean[string, bool](s, opts.Boolean)
	case []byte:
		return textual2boolean[[]byte, bool](s, opts.Boolean)
	case []rune:
		return textual2boolean[[]rune, bool](s, opts.Boolean)

	default:
		// 反射兜底
		return reflectToBoolean(src, opts)
	}
}

// 数值转换统一处理 - 利用泛型约束
func convertToNumeric[D Numeric](src any, opts *Options, dstInfo TypeInfo) (D, error) {
	switch s := src.(type) {
	// 布尔转换
	case bool:
		return boolean2numeric[bool, D](s)

	// 数值间转换 - 使用优化的泛型函数
	case int:
		return numeric2numeric[int, D](s, opts.Numeric, dstInfo)
	case int8:
		return numeric2numeric[int8, D](s, opts.Numeric, dstInfo)
	case int16:
		return numeric2numeric[int16, D](s, opts.Numeric, dstInfo)
	case int32:
		return numeric2numeric[int32, D](s, opts.Numeric, dstInfo)
	case int64:
		return numeric2numeric[int64, D](s, opts.Numeric, dstInfo)
	case uint:
		return numeric2numeric[uint, D](s, opts.Numeric, dstInfo)
	case uint8:
		return numeric2numeric[uint8, D](s, opts.Numeric, dstInfo)
	case uint16:
		return numeric2numeric[uint16, D](s, opts.Numeric, dstInfo)
	case uint32:
		return numeric2numeric[uint32, D](s, opts.Numeric, dstInfo)
	case uint64:
		return numeric2numeric[uint64, D](s, opts.Numeric, dstInfo)
	case uintptr:
		return numeric2numeric[uintptr, D](s, opts.Numeric, dstInfo)
	case float32:
		return numeric2numeric[float32, D](s, opts.Numeric, dstInfo)
	case float64:
		return numeric2numeric[float64, D](s, opts.Numeric, dstInfo)

	// 复数转换
	case complex64:
		return complex2numeric[complex64, D](s)
	case complex128:
		return complex2numeric[complex128, D](s)

	// 文本转换
	case string:
		return textual2numeric[string, D](s, opts.Numeric, dstInfo)
	case []byte:
		return textual2numeric[[]byte, D](s, opts.Numeric, dstInfo)
	case []rune:
		return textual2numeric[[]rune, D](s, opts.Numeric, dstInfo)

	default:
		// 反射兜底
		return reflectToNumeric[D](src, opts, dstInfo)
	}
}

// 复数转换统一处理
func convertToComplex[D Complex](src any, opts *Options) (D, error) {
	switch s := src.(type) {
	case bool:
		return boolean2complex[bool, D](s)
	case int:
		return numeric2complex[int, D](s)
	case int8:
		return numeric2complex[int8, D](s)
	case int16:
		return numeric2complex[int16, D](s)
	case int32:
		return numeric2complex[int32, D](s)
	case int64:
		return numeric2complex[int64, D](s)
	case uint:
		return numeric2complex[uint, D](s)
	case uint8:
		return numeric2complex[uint8, D](s)
	case uint16:
		return numeric2complex[uint16, D](s)
	case uint32:
		return numeric2complex[uint32, D](s)
	case uint64:
		return numeric2complex[uint64, D](s)
	case uintptr:
		return numeric2complex[uintptr, D](s)
	case float32:
		return numeric2complex[float32, D](s)
	case float64:
		return numeric2complex[float64, D](s)
	case complex64:
		return complex2complex[complex64, D](s)
	case complex128:
		return complex2complex[complex128, D](s)
	case string:
		return textual2complex[string, D](s)
	case []byte:
		return textual2complex[[]byte, D](s)
	case []rune:
		return textual2complex[[]rune, D](s)
	default:
		return reflectToComplex[D](src, opts)
	}
}

// 文本转换统一处理
func convertToTextual[D Textual](src any, opts *Options) (D, error) {
	switch s := src.(type) {
	case bool:
		return boolean2textual[bool, D](s)
	case int:
		return numeric2textual[int, D](s)
	case int8:
		return numeric2textual[int8, D](s)
	case int16:
		return numeric2textual[int16, D](s)
	case int32:
		return numeric2textual[int32, D](s)
	case int64:
		return numeric2textual[int64, D](s)
	case uint:
		return numeric2textual[uint, D](s)
	case uint8:
		return numeric2textual[uint8, D](s)
	case uint16:
		return numeric2textual[uint16, D](s)
	case uint32:
		return numeric2textual[uint32, D](s)
	case uint64:
		return numeric2textual[uint64, D](s)
	case uintptr:
		return numeric2textual[uintptr, D](s)
	case float32:
		return numeric2textual[float32, D](s)
	case float64:
		return numeric2textual[float64, D](s)
	case complex64:
		return complex2textual[complex64, D](s)
	case complex128:
		return complex2textual[complex128, D](s)
	case string:
		return textual2textual[string, D](s)
	case []byte:
		return textual2textual[[]byte, D](s)
	case []rune:
		return textual2textual[[]rune, D](s)
	default:
		return reflectToTextual[D](src, opts)
	}
}
