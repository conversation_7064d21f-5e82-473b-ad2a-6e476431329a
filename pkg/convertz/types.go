// Package convertz provides high-performance type conversion utilities
// using Go 1.18+ generics for compile-time optimization and type safety.
package convertz

import (
	"errors"
	"math/bits"
)

// 错误定义
var (
	ErrOverflow    = errors.New("value overflow")
	ErrUnsupported = errors.New("unsupported type conversion")
	ErrInvalidSrc  = errors.New("invalid source value")
)

// 基础类型约束 - 利用Go 1.18+的类型约束特性
type (
	// Boolean 约束所有布尔类型
	Boolean interface {
		~bool
	}

	// SignedInt 约束有符号整数类型
	SignedInt interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64
	}

	// UnsignedInt 约束无符号整数类型
	UnsignedInt interface {
		~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr
	}

	// Integer 约束所有整数类型
	Integer interface {
		SignedInt | UnsignedInt
	}

	// Float 约束浮点数类型
	Float interface {
		~float32 | ~float64
	}

	// Numeric 约束所有数值类型
	Numeric interface {
		Integer | Float
	}

	// Complex 约束复数类型
	Complex interface {
		~complex64 | ~complex128
	}

	// Textual 约束文本类型
	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

// 类型信息结构 - 编译时计算，运行时零成本
type TypeInfo struct {
	Kind     TypeKind
	BitSize  int
	IsSigned bool
}

type TypeKind uint8

const (
	KindBool TypeKind = iota
	KindSignedInt
	KindUnsignedInt
	KindFloat
	KindComplex
	KindString
	KindBytes
	KindRunes
	KindUnknown
)

// 编译时类型信息计算 - 利用泛型的编译时特性
func getTypeInfo[T any]() TypeInfo {
	var zero T
	switch any(zero).(type) {
	case bool:
		return TypeInfo{Kind: KindBool, BitSize: 1}
	case int:
		return TypeInfo{Kind: KindSignedInt, BitSize: bits.UintSize, IsSigned: true}
	case int8:
		return TypeInfo{Kind: KindSignedInt, BitSize: 8, IsSigned: true}
	case int16:
		return TypeInfo{Kind: KindSignedInt, BitSize: 16, IsSigned: true}
	case int32:
		return TypeInfo{Kind: KindSignedInt, BitSize: 32, IsSigned: true}
	case int64:
		return TypeInfo{Kind: KindSignedInt, BitSize: 64, IsSigned: true}
	case uint:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: bits.UintSize, IsSigned: false}
	case uint8:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: 8, IsSigned: false}
	case uint16:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: 16, IsSigned: false}
	case uint32:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: 32, IsSigned: false}
	case uint64:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: 64, IsSigned: false}
	case uintptr:
		return TypeInfo{Kind: KindUnsignedInt, BitSize: bits.UintSize, IsSigned: false}
	case float32:
		return TypeInfo{Kind: KindFloat, BitSize: 32, IsSigned: true}
	case float64:
		return TypeInfo{Kind: KindFloat, BitSize: 64, IsSigned: true}
	case complex64:
		return TypeInfo{Kind: KindComplex, BitSize: 64}
	case complex128:
		return TypeInfo{Kind: KindComplex, BitSize: 128}
	case string:
		return TypeInfo{Kind: KindString}
	case []byte:
		return TypeInfo{Kind: KindBytes}
	case []rune:
		return TypeInfo{Kind: KindRunes}
	default:
		return TypeInfo{Kind: KindUnknown}
	}
}

// 选项类型定义 - 保持向后兼容
type (
	Option  func(*Options)
	Options struct {
		Boolean *BooleanOptions
		Numeric *NumericOptions
		Complex *ComplexOptions
		Textual *TextualOptions
	}

	BooleanOptions struct {
		TrueSet  map[string]struct{}
		FalseSet map[string]struct{}
	}

	NumericOptions struct {
		Strict bool
	}

	ComplexOptions struct{}

	TextualOptions struct{}
)

// 默认选项 - 使用单例模式优化内存
var defaultOptions = &Options{
	Boolean: &BooleanOptions{},
	Numeric: &NumericOptions{},
	Complex: &ComplexOptions{},
	Textual: &TextualOptions{},
}

func DefaultOptions() *Options {
	return &Options{
		Boolean: &BooleanOptions{},
		Numeric: &NumericOptions{},
		Complex: &ComplexOptions{},
		Textual: &TextualOptions{},
	}
}
