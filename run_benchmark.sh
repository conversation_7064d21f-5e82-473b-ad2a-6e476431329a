#!/bin/bash

# Converts vs Convertz 性能基准测试脚本

echo "🚀 开始运行 Converts vs Convertz 性能基准测试..."
echo "=================================================="

# 创建结果目录
mkdir -p benchmark_results

# 运行基准测试并保存结果
echo "📊 运行基准测试..."
go test -bench=. -benchmem -count=5 -timeout=30m > benchmark_results/raw_results.txt 2>&1

# 检查测试是否成功
if [ $? -eq 0 ]; then
    echo "✅ 基准测试完成！"
else
    echo "❌ 基准测试失败，请检查错误信息："
    cat benchmark_results/raw_results.txt
    exit 1
fi

# 分析结果
echo "📈 分析测试结果..."

# 提取 Converts 包的结果
echo "=== Converts 包性能结果 ===" > benchmark_results/converts_results.txt
grep "Converts" benchmark_results/raw_results.txt >> benchmark_results/converts_results.txt

# 提取 Convertz 包的结果
echo "=== Convertz 包性能结果 ===" > benchmark_results/convertz_results.txt
grep "Convertz" benchmark_results/raw_results.txt >> benchmark_results/convertz_results.txt

# 生成对比报告
cat << 'EOF' > benchmark_results/generate_report.go
package main

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
)

type BenchResult struct {
	Name     string
	NsPerOp  float64
	BytesPerOp int64
	AllocsPerOp int64
}

func main() {
	convertsResults := parseBenchmarkFile("benchmark_results/converts_results.txt")
	convertzResults := parseBenchmarkFile("benchmark_results/convertz_results.txt")
	
	generateComparisonReport(convertsResults, convertzResults)
}

func parseBenchmarkFile(filename string) map[string]BenchResult {
	results := make(map[string]BenchResult)
	
	file, err := os.Open(filename)
	if err != nil {
		return results
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	benchRegex := regexp.MustCompile(`Benchmark(\w+)\s+\d+\s+([\d.]+)\s+ns/op(?:\s+([\d.]+)\s+B/op)?(?:\s+([\d.]+)\s+allocs/op)?`)
	
	for scanner.Scan() {
		line := scanner.Text()
		matches := benchRegex.FindStringSubmatch(line)
		if len(matches) >= 3 {
			name := matches[1]
			nsPerOp, _ := strconv.ParseFloat(matches[2], 64)
			
			var bytesPerOp, allocsPerOp int64
			if len(matches) > 3 && matches[3] != "" {
				bytesPerOp, _ = strconv.ParseInt(matches[3], 10, 64)
			}
			if len(matches) > 4 && matches[4] != "" {
				allocsPerOp, _ = strconv.ParseInt(matches[4], 10, 64)
			}
			
			results[name] = BenchResult{
				Name:        name,
				NsPerOp:     nsPerOp,
				BytesPerOp:  bytesPerOp,
				AllocsPerOp: allocsPerOp,
			}
		}
	}
	
	return results
}

func generateComparisonReport(converts, convertz map[string]BenchResult) {
	fmt.Println("# Converts vs Convertz 性能对比报告")
	fmt.Println("=" * 60)
	fmt.Println()
	
	fmt.Printf("%-30s %15s %15s %10s %15s %15s %10s\n", 
		"测试项目", "Converts(ns/op)", "Convertz(ns/op)", "速度提升", 
		"Converts(B/op)", "Convertz(B/op)", "内存优化")
	fmt.Println(strings.Repeat("-", 120))
	
	totalSpeedImprovement := 0.0
	totalMemoryImprovement := 0.0
	testCount := 0
	
	for name, convertsResult := range converts {
		// 匹配对应的 convertz 结果
		convertzName := strings.Replace(name, "_Converts", "_Convertz", 1)
		if convertzResult, exists := convertz[convertzName]; exists {
			speedImprovement := convertsResult.NsPerOp / convertzResult.NsPerOp
			memoryImprovement := float64(convertsResult.BytesPerOp) / float64(max(convertzResult.BytesPerOp, 1))
			
			fmt.Printf("%-30s %15.2f %15.2f %9.2fx %15d %15d %9.2fx\n",
				strings.Replace(name, "_Converts", "", 1),
				convertsResult.NsPerOp, convertzResult.NsPerOp, speedImprovement,
				convertsResult.BytesPerOp, convertzResult.BytesPerOp, memoryImprovement)
			
			totalSpeedImprovement += speedImprovement
			totalMemoryImprovement += memoryImprovement
			testCount++
		}
	}
	
	fmt.Println(strings.Repeat("-", 120))
	fmt.Printf("%-30s %31s %9.2fx %31s %9.2fx\n", 
		"平均提升", "", totalSpeedImprovement/float64(testCount), 
		"", totalMemoryImprovement/float64(testCount))
	
	fmt.Println()
	fmt.Println("## 总结")
	fmt.Printf("- 平均速度提升: %.2fx\n", totalSpeedImprovement/float64(testCount))
	fmt.Printf("- 平均内存优化: %.2fx\n", totalMemoryImprovement/float64(testCount))
	fmt.Printf("- 测试项目数量: %d\n", testCount)
}

func max(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}
EOF

# 运行报告生成器
cd benchmark_results
go run generate_report.go > comparison_report.txt
cd ..

echo "📋 生成性能对比报告..."
cat benchmark_results/comparison_report.txt

echo ""
echo "🎉 基准测试完成！详细结果保存在 benchmark_results/ 目录中"
echo "   - raw_results.txt: 原始测试结果"
echo "   - converts_results.txt: Converts 包结果"
echo "   - convertz_results.txt: Convertz 包结果"  
echo "   - comparison_report.txt: 性能对比报告"
