package main

import (
	"testing"

	"peanut/pkg/converts"
	"peanut/pkg/convertz"
)

// ============================================================================
// 基础转换性能测试
// ============================================================================

// String to Int 转换测试
func BenchmarkStringToInt_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[int]("123")
	}
}

func BenchmarkStringToInt_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[int]("123")
	}
}

// Int to String 转换测试
func BenchmarkIntToString_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](123)
	}
}

func BenchmarkIntToString_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](123)
	}
}

// Bool to Int 转换测试
func BenchmarkBoolToInt_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[int](true)
	}
}

func BenchmarkBoolToInt_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[int](true)
	}
}

// Float to String 转换测试
func BenchmarkFloatToString_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](3.14159)
	}
}

func BenchmarkFloatToString_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](3.14159)
	}
}

// ============================================================================
// 类型特定API测试
// ============================================================================

// ToNumeric API 测试
func BenchmarkToNumeric_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.ToNumeric[int]("123", nil)
	}
}

func BenchmarkToNumeric_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.ToNumeric[int]("123", nil)
	}
}

// ToBoolean API 测试
func BenchmarkToBoolean_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.ToBoolean[bool]("true", nil)
	}
}

func BenchmarkToBoolean_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.ToBoolean[bool]("true", nil)
	}
}

// ToTextual API 测试
func BenchmarkToTextual_Converts(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.ToTextual[string](123, nil)
	}
}

func BenchmarkToTextual_Convertz(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.ToTextual[string](123, nil)
	}
}

// ============================================================================
// 复杂转换测试
// ============================================================================

// 字节切片到字符串转换 (零拷贝优化测试)
func BenchmarkBytesToString_Converts(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](data)
	}
}

func BenchmarkBytesToString_Convertz(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](data)
	}
}

// 字符串到字节切片转换
func BenchmarkStringToBytes_Converts(b *testing.B) {
	str := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[[]byte](str)
	}
}

func BenchmarkStringToBytes_Convertz(b *testing.B) {
	str := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[[]byte](str)
	}
}

// 复数转换测试
func BenchmarkComplexToString_Converts(b *testing.B) {
	c := complex(3.14, 2.71)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](c)
	}
}

func BenchmarkComplexToString_Convertz(b *testing.B) {
	c := complex(3.14, 2.71)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](c)
	}
}

// ============================================================================
// 严格模式测试
// ============================================================================

// 数值严格模式转换
func BenchmarkNumericStrict_Converts(b *testing.B) {
	opts := &converts.NumericOptions{Strict: true}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.ToNumeric[int8]("100", opts)
	}
}

func BenchmarkNumericStrict_Convertz(b *testing.B) {
	opts := &convertz.NumericOptions{Strict: true}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.ToNumeric[int8]("100", opts)
	}
}

// ============================================================================
// 反射兜底测试
// ============================================================================

// 自定义类型转换 (触发反射)
type CustomInt int

func BenchmarkCustomType_Converts(b *testing.B) {
	var custom CustomInt = 123
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](custom)
	}
}

func BenchmarkCustomType_Convertz(b *testing.B) {
	var custom CustomInt = 123
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](custom)
	}
}

// ============================================================================
// 批量转换测试
// ============================================================================

func BenchmarkBatchConversion_Converts(b *testing.B) {
	data := []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, s := range data {
			_, _ = converts.To[int](s)
		}
	}
}

func BenchmarkBatchConversion_Convertz(b *testing.B) {
	data := []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, s := range data {
			_, _ = convertz.To[int](s)
		}
	}
}

// ============================================================================
// 内存分配测试
// ============================================================================

func BenchmarkMemoryAllocation_Converts(b *testing.B) {
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converts.To[string](123)
	}
}

func BenchmarkMemoryAllocation_Convertz(b *testing.B) {
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = convertz.To[string](123)
	}
}

// ============================================================================
// 并发测试
// ============================================================================

func BenchmarkConcurrent_Converts(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = converts.To[int]("123")
		}
	})
}

func BenchmarkConcurrent_Convertz(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = convertz.To[int]("123")
		}
	})
}
